---
alwaysApply: true
---
description: Core coding conventions for the Civility MVP (Android + Cloud backend)
alwaysApply: true

Civility Coding Rules

These rules are injected into Cursor so the AI consistently follows our project standards across Android, backend (Go), and shared infrastructure.

Kotlin First, Compose Everywhere

Source files live under app/src/main/kotlin/**.

Use Jetpack Compose for all UI; no XML layouts.

Name Composable functions PascalCase and end with Screen, Card, or Row as appropriate (LoginScreen, MessageBubble).

Coroutines over Callbacks

Use suspend functions and ViewModelScope for async work.

No GlobalScope or callback pyramids; wrap one‑shot tasks in withContext(Dispatchers.IO).

Network Layer = Retrofit + OkHttp3 + Moshi

All REST interfaces live in net/**/*.kt.

Enforce timeouts: connect=10s, read/write=30s.

Add an HttpLoggingInterceptor only in DEBUG builds.

Crypto Canon: Signal‑Protocol Only

All message bodies are encrypted with libsignal-protocol-java before leaving the device.

Never log or persist plaintext; use ByteArray not String when possible.

Repository Pattern for Data

ViewModels talk only to Repository interfaces, which mediate Firestore and local Room cache.

No direct Firestore calls in UI layer.

Backend in Go (Gin) follows JSON‑only API

All endpoints respond with {'status':'ok','data':...} or {'error':'...', 'code':...}.

Input validation via github.com/go-playground/validator/v10.

Panic handlers return HTTP 500 with request ID.

Secrets via GCP Secret Manager / Env Vars

No API keys in source.

Local dev uses direnv to load .envrc; CI injects vars via GitHub Secrets.

Error Handling & Logging

Android: wrap network results in Result<T> sealed class (Success, Error).

Backend: use structured logs (zap) with traceID, userID fields.

Never swallow exceptions—surface to Sentry/Firebase Crashlytics.

Modular Structure Required

Limit source files to a single responsibility; keep them generally under 300 lines.

Organize code into clear modules/packages: ui, data, network, crypto, backend, etc.

Cursor completions should refuse to add unrelated classes or structs to an existing file—create a new file instead.

