# Civility — MVP Tech‑Stack

*A deliberately minimal, battle‑tested stack that hits all functional and non‑functional requirements with the lowest possible ops overhead.*

---

## 1. High‑Level Diagram

```mermaid
graph TD
    subgraph Mobile
        A[Android App<br/>(<PERSON><PERSON><PERSON> + Compose)]
    end
    subgraph Cloud (GCP)
        B[Cloud Run<br/>(Go or Node)]
        C[Firestore<br/>Native Mode]
        D[Secret Manager]
        E[Vertex AI<br/>Gemini 2.5 Flash‑Lite]
    end
    A -- HTTPS (E2EE Signal) --> B
    B -- gRPC / REST --> E
    B -- read/write --> C
    B -- fetch --> D
    B -- push FCM token --> F[Firebase Cloud Messaging]
    F -- notification --> A
```

---

## 2. Client (Android)

| Layer       | Tech                               | Why it’s the simplest reliable pick     |
| ----------- | ---------------------------------- | --------------------------------------- |
| Language    | **Kotlin**                         | Native, concise, first‑class support    |
| UI          | **Jetpack Compose**                | Eliminates XML boilerplate; reactive    |
| Crypto      | **libsignal‑protocol‑java**        | Proven E2EE (used by WhatsApp)          |
| Networking  | **Retrofit + OkHttp**              | Widely adopted, drop‑in TLS/HTTP2       |
| Push        | **Firebase Cloud Messaging (FCM)** | Free, standard on Android               |
| Local store | **Room DB**                        | Simple SQL‑lite wrapper for cached msgs |

---

## 3. Backend (Serverless)

| Component      | Tech                                     | Rationale                                                            |
| -------------- | ---------------------------------------- | -------------------------------------------------------------------- |
| Runtime        | **Cloud Run**                            | Scale‑to‑zero, free tier covers dev; cold‑start < 1 s with 256 MiB   |
| Container      | **Go (Gin) *****or***** Node (Fastify)** | Both tiny image (<50 MB); pick team comfort                          |
| Database       | **Firestore (Native mode)**              | Auto‑scales, doc‑store fits chat verdicts, free quota ample for beta |
| Secrets        | **Secret Manager**                       | Zero code change to rotate keys                                      |
| Scheduled jobs | **Cloud Functions (2nd gen)**            | On‑demand PDF/JSON export; share GCP IAM                             |

---

## 4. Moderation LLM

| Item      | Choice                               | Notes                            |
| --------- | ------------------------------------ | -------------------------------- |
| Model     | **Gemini 2.5 Flash‑Lite**            | Pay‑per‑token; 60‑150 ms latency |
| Endpoint  | **Vertex AI, us‑central1**           | Reduces cross‑region latency     |
| Prompting | System prompt + few‑shot JSON schema | Deterministic `temperature: 0`   |

---

## 5. DevOps & CI/CD

| Stage       | Tool                          | Purpose                           |
| ----------- | ----------------------------- | --------------------------------- |
| Build       | **GitHub Actions**            | Lint → unit tests → Docker build  |
| Registry    | **Artifact Registry**         | 0.5 GB free; single backend image |
| Deploy      | **gcloud deploy (CLI)**       | `gcloud run deploy` from CI       |
| Mobile beta | **Firebase App Distribution** | Push APK to internal testers      |

---

## 6. Observability

- **Cloud Logging** (30‑day retention) for backend traces
- **Cloud Monitoring** (Uptime checks + Alerting): p95 latency & error‑rate SLIs
- **Crashlytics** for Android runtime exceptions

---

## 7. PDF/JSON Export Pipeline

1. User taps *Export* in app → signed URL to Cloud Run.
2. Cloud Run spawns a **Cloud Function (2nd gen)** job.
3. Function pulls messages & verdicts from Firestore.
4. Generates PDF via **pdf‑lib** (Node) or **go‑pdf** (Go).
5. Stores file in **Cloud Storage** (signed, 24 h expiry) → returns link.

---

## 8. Cost Snap‑Shot (Dev/Test)

| Service           | Free‑tier headroom                              |
| ----------------- | ----------------------------------------------- |
| Cloud Run         | 2 M req / 180 k vCPU‑s / 360 k GiB‑s            |
| Firestore         | 1 GiB storage, 50 k reads & 20 k writes per day |
| Vertex AI         | Pay‑per‑token only (≈ \$0.45 per 100 k msgs)    |
| Artifact Registry | 0.5 GB image storage                            |
| FCM & Crashlytics | Unlimited free                                  |

Total expected spend during MVP pilot (≤ 25 users) **< \$1/month**.

---

## 9. “Why Not …?” Quick Answers

- **Kubernetes** → Overkill; Cloud Run already handles autoscale.
- **SQL (Postgres)** → Firestore avoids schema management and is free at low volume.
- **Custom ML model** → Flash‑Lite is cheaper and more nuanced than open‑source BERT with a GPU.

---

*Last updated: 2025‑07‑01*

