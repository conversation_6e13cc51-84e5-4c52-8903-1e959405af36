# Civility — Implementation Plan (MVP)

> **Focus:** deliver core 1‑to‑1 chat with Gemini moderation and E2EE. Nice‑to‑have features (auto‑rewrite mode, observer roles, etc.) come in Phase 2.

| Step | Task (small & focused) | Simple “Done” Test |
| ---- | ---------------------- | ------------------- |
| **1** | **Repo & CI bootstrap** – Create monorepo (`/android`, `/backend`) + GitHub Actions pipeline (lint → unit tests → Docker build). | Push a PR with `README.md`; CI passes all jobs. |
| **2** | **Android skeleton** – Init Kotlin/Compose project, single `MainActivity`. | `./gradlew installDebug` launches blank app on Pixel 8. |
| **3** | **Signal key setup** – Generate and persist Curve25519 identity + session keys (libsignal‑protocol‑java). | Unit test: serialize keys, reload, compare public key bytes. |
| **4** | **Minimal Cloud Run backend** – Gin/Fastify server with `/healthz` endpoint. | `curl https://<url>/healthz` returns `{status:"ok"}` with 200. |
| **5** | **Send/receive plaintext messages (no moderation yet)** – Retrofit POST to `/msg`, backend echoes with timestamp. | Two emulators exchange echoed text; messages display in UI. |
| **6** | **Encrypt transport** – Wrap messages in Signal cipher before POST; backend stores only ciphertext. | Logcat shows ciphertext payload; server DB doc has no readable text. |
| **7** | **Gemini moderation hook** – Backend calls Vertex AI, returns `allow|warn|block`. | Integration test: POST “You jerk” → JSON `block`; POST “Pick up at 5” → `allow`. |
| **8** | **Warn/Block UX** – In Compose: yellow banner for `warn`, modal for `block` with “Edit & Resend”. | Manual test: flagged sentence shows correct UI variant. |
| **9** | **Store verdict + hash in Firestore** – Save `{hash, verdict, ts}` per msg. | Firestore console shows new doc after each send; `hash` = SHA‑256(msg). |
| **10** | **Push notifications (FCM)** – Backend sends FCM to recipient on allowed msg. | Phone locked: notification arrives within 5 s. |
| **11** | **Basic PDF export** – Cloud Function builds PDF from Firestore docs and returns signed URL. | Tap *Export* → file downloads; opening PDF shows chat transcript. |
| **12** | **Pilot readiness hardening** – Crashlytics enabled, p95 latency alert, secrets in Secret Manager. | Trigger test crash, see entry in Crashlytics; Cloud Monitoring shows uptime check green. |

---

## Technical Specifications

### Authentication System
**Flow:** Email + OTP → Device Registration → Key Generation
```
1. User enters email → Backend sends 6-digit OTP via SendGrid
2. OTP verification → Backend creates user record in Firestore
3. App generates Signal identity key pair in Android Keystore
4. Public key uploaded to backend user profile
5. Biometric unlock configured (optional)
```

**API Endpoints:**
- `POST /auth/request-otp` → `{email}` → `{success, message}`
- `POST /auth/verify-otp` → `{email, otp}` → `{jwt_token, user_id}`
- `POST /auth/register-device` → `{public_key, fcm_token}` → `{success}`

**Firestore Schema:**
```
users/{user_id}
├── email: string
├── created_at: timestamp
├── public_key: base64
├── fcm_token: string
└── status: "active" | "suspended"
```

### Messaging System
**Signal Protocol Implementation:**
```
1. Sender encrypts: plaintext → Signal.encrypt(recipient_public_key) → ciphertext
2. Backend receives: {to_user_id, ciphertext, sender_signature}
3. Moderation: decrypt → analyze → re-encrypt if allowed
4. Delivery: FCM push → recipient decrypts locally
```

**Message States:**
- `pending_moderation` → Gemini analysis in progress
- `blocked` → Sender can edit and resend
- `warned` → Sender can override and send
- `delivered` → Recipient can view (collapsed if toxic)
- `read` → Read receipt sent

**API Endpoints:**
- `POST /messages/send` → `{to_user_id, ciphertext}` → `{message_id, status}`
- `GET /messages/inbox` → `{messages: [{id, from, ciphertext, verdict, timestamp}]}`
- `POST /messages/mark-read` → `{message_id}` → `{success}`

### Moderation System
**Gemini Integration:**
```
System Prompt: "Analyze this co-parenting message for toxicity. Respond with JSON only."
User Message: [decrypted text]
Expected Response: {
  "decision": "allow" | "warn" | "block",
  "labels": ["hostile", "manipulative", "threatening"],
  "confidence": 0.85
}
```

**Verdict Storage:**
```
verdicts/{message_id}
├── hash: sha256(original_message)
├── decision: string
├── labels: array
├── confidence: number
├── timestamp: timestamp
└── model_version: "gemini-2.5-flash-lite"
```

**Error Handling:**
- Gemini timeout (>5s) → fail open with warning
- API quota exceeded → queue for retry, notify sender
- Invalid JSON response → log error, default to "warn"

---

## Complete Data Models & API Contracts

### Firestore Collections Schema

#### Users Collection
```typescript
users/{user_id} {
  email: string;                    // "<EMAIL>"
  created_at: Timestamp;
  updated_at: Timestamp;
  public_key: string;               // Base64 Signal identity key
  fcm_token: string;                // Firebase messaging token
  status: "active" | "suspended" | "deleted";
  profile: {
    display_name: string;           // "Sarah M."
    timezone: string;               // "America/New_York"
  };
  settings: {
    biometric_enabled: boolean;
    notification_enabled: boolean;
    auto_collapse_toxic: boolean;   // Default: true
  };
  // Co-parent relationship
  connected_user_id?: string;       // Other parent's user_id
  connection_status: "pending" | "connected" | "blocked";
}
```

#### Messages Collection
```typescript
messages/{message_id} {
  from_user_id: string;
  to_user_id: string;
  ciphertext: string;               // Signal-encrypted message
  sender_signature: string;         // Message authenticity
  created_at: Timestamp;
  status: "pending_moderation" | "blocked" | "warned" | "delivered" | "read";
  verdict_id?: string;              // Reference to verdicts collection
  // Metadata for export/legal purposes
  sequence_number: number;          // Ordered within conversation
  reply_to_message_id?: string;     // For threading (future)
}
```

#### Verdicts Collection
```typescript
verdicts/{verdict_id} {
  message_id: string;
  hash: string;                     // SHA-256 of original plaintext
  decision: "allow" | "warn" | "block";
  labels: string[];                 // ["hostile", "manipulative"]
  confidence: number;               // 0.0 - 1.0
  model_version: string;            // "gemini-2.5-flash-lite"
  processing_time_ms: number;       // For performance monitoring
  created_at: Timestamp;
  // Legal retention (7 years)
  retention_until: Timestamp;
}
```

#### Conversations Collection (Derived)
```typescript
conversations/{conversation_id} {  // conversation_id = sorted(user1_id, user2_id).join("_")
  participants: string[];           // [user1_id, user2_id]
  created_at: Timestamp;
  last_message_at: Timestamp;
  last_message_preview: string;     // Encrypted preview for notifications
  message_count: number;
  unread_count: {
    [user_id: string]: number;
  };
}
```

#### Export Jobs Collection
```typescript
export_jobs/{job_id} {
  user_id: string;                  // Who requested export
  conversation_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  date_range: {
    start: Timestamp;
    end: Timestamp;
  };
  file_url?: string;                // Signed Cloud Storage URL
  expires_at?: Timestamp;           // 24h expiry
  created_at: Timestamp;
  completed_at?: Timestamp;
}
```

### Complete API Specification

#### Authentication Endpoints

**POST /auth/request-otp**
```typescript
Request: {
  email: string;                    // Valid email format
}
Response: {
  success: boolean;
  message: string;                  // "OTP sent to email"
  rate_limit_reset?: number;        // Unix timestamp if rate limited
}
Errors: 400 (invalid email), 429 (rate limit), 500 (send failure)
```

**POST /auth/verify-otp**
```typescript
Request: {
  email: string;
  otp: string;                      // 6-digit code
}
Response: {
  success: boolean;
  jwt_token: string;                // 24h expiry
  user_id: string;
  is_new_user: boolean;             // First time login
}
Errors: 400 (invalid OTP), 401 (expired), 429 (rate limit)
```

**POST /auth/register-device**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  public_key: string;               // Base64 Signal identity key
  fcm_token: string;
  device_info: {
    platform: "android";
    app_version: string;
    os_version: string;
  };
}
Response: {
  success: boolean;
  user_profile: UserProfile;        // Full user object
}
```

**POST /auth/connect-coparent**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  coparent_email: string;
}
Response: {
  success: boolean;
  connection_status: "pending" | "connected";
  message: string;
}
```

#### Messaging Endpoints

**POST /messages/send**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  to_user_id: string;
  ciphertext: string;               // Signal-encrypted message
  sender_signature: string;
  client_timestamp: number;         // Unix timestamp
}
Response: {
  message_id: string;
  status: "pending_moderation" | "blocked" | "warned" | "delivered";
  verdict?: {
    decision: string;
    labels: string[];
    confidence: number;
  };
  estimated_delivery_time?: number; // Seconds
}
Errors: 400 (invalid payload), 403 (blocked user), 413 (message too long)
```

**GET /messages/conversation/{conversation_id}**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Query: {
  limit?: number;                   // Default: 50, Max: 100
  before_timestamp?: number;        // Pagination
  include_verdicts?: boolean;       // Default: false
}
Response: {
  messages: Message[];
  has_more: boolean;
  next_cursor?: string;
}
```

**POST /messages/mark-read**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  message_ids: string[];            // Batch mark as read
}
Response: {
  success: boolean;
  updated_count: number;
}
```

**POST /messages/override-warning**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  message_id: string;               // Message with "warned" status
  user_acknowledged: boolean;       // User confirmed they want to send
}
Response: {
  success: boolean;
  new_status: "delivered";
}
```

#### Export & Legal Endpoints

**POST /export/request**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  conversation_id: string;
  date_range: {
    start: string;                  // ISO 8601 date
    end: string;
  };
  format: "pdf" | "json" | "both";
  include_verdicts: boolean;        // Include moderation data
}
Response: {
  job_id: string;
  estimated_completion: number;     // Seconds
  status: "pending";
}
```

**GET /export/status/{job_id}**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Response: {
  job_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  progress_percent?: number;        // 0-100
  file_urls?: {
    pdf?: string;                   // Signed URL, 24h expiry
    json?: string;
  };
  error_message?: string;
  expires_at?: string;              // ISO 8601
}
```

#### Admin & Monitoring Endpoints

**GET /health**
```typescript
Response: {
  status: "ok" | "degraded" | "down";
  timestamp: number;
  services: {
    firestore: "ok" | "error";
    gemini: "ok" | "error";
    fcm: "ok" | "error";
  };
  version: string;
}
```

**GET /metrics** (Internal only)
```typescript
Headers: { X-API-Key: "<internal_key>" }
Response: {
  messages_per_minute: number;
  moderation_latency_p95: number;   // Milliseconds
  error_rate: number;               // 0.0 - 1.0
  active_users_24h: number;
  gemini_cost_today: number;        // USD
}
```

### Error Response Format
```typescript
// All endpoints return errors in this format
ErrorResponse: {
  error: {
    code: string;                   // "INVALID_REQUEST", "RATE_LIMITED"
    message: string;                // Human-readable description
    details?: any;                  // Additional context
    request_id: string;             // For debugging
  };
}
```

### WebSocket Events (Real-time)
```typescript
// Client subscribes to: /ws/conversations/{conversation_id}

// Incoming message
{
  type: "message_received";
  data: {
    message_id: string;
    from_user_id: string;
    ciphertext: string;
    timestamp: number;
    verdict?: Verdict;
  };
}

// Typing indicator
{
  type: "typing_start" | "typing_stop";
  data: {
    user_id: string;
    timestamp: number;
  };
}

// Read receipt
{
  type: "message_read";
  data: {
    message_id: string;
    read_by: string;
    timestamp: number;
  };
}
```
```
```

## Post‑MVP / Phase 2 (not in this sprint)
- Sender suggested rewrites & auto‑rewrite modes.
- Observer (therapist/legal) read‑only accounts.
- Group chat & media attachments.
- Optional PII redaction toggle.

*Last updated: 2025‑07‑01*

