# Civility — Implementation Plan (MVP)

> **Focus:** deliver core 1‑to‑1 chat with Gemini moderation and E2EE. Nice‑to‑have features (auto‑rewrite mode, observer roles, etc.) come in Phase 2.

| Step | Task (small & focused) | Simple “Done” Test |
| ---- | ---------------------- | ------------------- |
| **1** | **Repo & CI bootstrap** – Create monorepo (`/android`, `/backend`) + GitHub Actions pipeline (lint → unit tests → Docker build). | Push a PR with `README.md`; CI passes all jobs. |
| **2** | **Android skeleton** – Init Kotlin/Compose project, single `MainActivity`. | `./gradlew installDebug` launches blank app on Pixel 8. |
| **3** | **Signal key setup** – Generate and persist Curve25519 identity + session keys (libsignal‑protocol‑java). | Unit test: serialize keys, reload, compare public key bytes. |
| **4** | **Minimal Cloud Run backend** – Gin/Fastify server with `/healthz` endpoint. | `curl https://<url>/healthz` returns `{status:"ok"}` with 200. |
| **5** | **Send/receive plaintext messages (no moderation yet)** – Retrofit POST to `/msg`, backend echoes with timestamp. | Two emulators exchange echoed text; messages display in UI. |
| **6** | **Encrypt transport** – Wrap messages in Signal cipher before POST; backend stores only ciphertext. | Logcat shows ciphertext payload; server DB doc has no readable text. |
| **7** | **Gemini moderation hook** – Backend calls Vertex AI, returns `allow|warn|block`. | Integration test: POST “You jerk” → JSON `block`; POST “Pick up at 5” → `allow`. |
| **8** | **Warn/Block UX** – In Compose: yellow banner for `warn`, modal for `block` with “Edit & Resend”. | Manual test: flagged sentence shows correct UI variant. |
| **9** | **Store verdict + hash in Firestore** – Save `{hash, verdict, ts}` per msg. | Firestore console shows new doc after each send; `hash` = SHA‑256(msg). |
| **10** | **Push notifications (FCM)** – Backend sends FCM to recipient on allowed msg. | Phone locked: notification arrives within 5 s. |
| **11** | **Basic PDF export** – Cloud Function builds PDF from Firestore docs and returns signed URL. | Tap *Export* → file downloads; opening PDF shows chat transcript. |
| **12** | **Pilot readiness hardening** – Crashlytics enabled, p95 latency alert, secrets in Secret Manager. | Trigger test crash, see entry in Crashlytics; Cloud Monitoring shows uptime check green. |

---

## Implementation Steps with Error Handling

### Step 1: Repo & CI Bootstrap
**Task:** Create monorepo (`/android`, `/backend`) + GitHub Actions pipeline (lint → unit tests → Docker build)

**Error Scenarios & Handling:**
- **Build failures**: Set up retry logic (3 attempts) with exponential backoff
- **Dependency conflicts**: Pin exact versions in `package.json`/`build.gradle`
- **Docker build timeouts**: Use multi-stage builds, cache layers
- **Secret management**: Fail fast if required secrets missing, clear error messages

**Edge Cases:**
- Large repository size → Use `.dockerignore` and `.gitignore` properly
- Multiple developers → Branch protection rules, required status checks
- CI quota limits → Implement build matrix optimization

**Acceptance Criteria:**
- [ ] Push a PR with `README.md`; CI passes all jobs
- [ ] Failed builds show clear error messages and suggested fixes
- [ ] Secrets are properly masked in logs
- [ ] Build time < 5 minutes for typical changes

### Step 2: Android Skeleton
**Task:** Init Kotlin/Compose project, single `MainActivity`

**Error Scenarios & Handling:**
- **Gradle sync failures**: Clear cache, retry with `--refresh-dependencies`
- **SDK version mismatches**: Use `compileSdk 34`, `minSdk 26` consistently
- **Emulator startup issues**: Fallback to different AVD, clear emulator data
- **Memory issues**: Configure proper heap sizes in `gradle.properties`

**Edge Cases:**
- Different Android versions → Test on API 26, 30, 34 minimum
- Various screen sizes → Use responsive layouts from start
- Low storage devices → Implement APK size monitoring

**Acceptance Criteria:**
- [ ] `./gradlew installDebug` launches blank app on Pixel 8
- [ ] App starts in <3 seconds on emulator
- [ ] No memory leaks detected with LeakCanary
- [ ] Builds successfully on clean machine

### Step 3: Signal Key Setup
**Task:** Generate and persist Curve25519 identity + session keys (libsignal‑protocol‑java)

**Error Scenarios & Handling:**
- **Keystore access denied**: Request permissions, fallback to encrypted SharedPrefs
- **Key generation failures**: Retry with different entropy sources
- **Biometric unavailable**: Graceful degradation to PIN/password
- **Key corruption**: Detect and regenerate with user consent

**Edge Cases:**
- Device factory reset → Key recovery impossible, clear user data
- Multiple app instances → Prevent concurrent key access
- Rooted devices → Additional security warnings
- Hardware security module unavailable → Software fallback with warnings

**Acceptance Criteria:**
- [ ] Unit test: serialize keys, reload, compare public key bytes
- [ ] Keys survive app restart and device reboot
- [ ] Biometric authentication works on supported devices
- [ ] Clear error messages for unsupported hardware

### Step 4: Minimal Cloud Run Backend
**Task:** Gin/Fastify server with `/healthz` endpoint

**Error Scenarios & Handling:**
- **Cold start timeouts**: Optimize container size, use minimal base image
- **Memory exhaustion**: Set appropriate limits, implement graceful degradation
- **Database connection failures**: Retry logic with circuit breaker pattern
- **Authentication failures**: Clear error codes, rate limiting

**Edge Cases:**
- High concurrent load → Auto-scaling configuration
- Network partitions → Implement health check grace periods
- Deployment failures → Blue-green deployment with rollback
- Resource quotas exceeded → Monitoring and alerting

**Acceptance Criteria:**
- [ ] `curl https://<url>/healthz` returns `{status:"ok"}` with 200
- [ ] Cold start time < 1 second
- [ ] Handles 100 concurrent requests without errors
- [ ] Proper logging for all error scenarios

### Step 5: Send/Receive Plaintext Messages
**Task:** Retrofit POST to `/msg`, backend echoes with timestamp (no moderation yet)

**Error Scenarios & Handling:**
- **Network timeouts**: Implement exponential backoff, show retry UI
- **Message ordering issues**: Use sequence numbers and client-side sorting
- **Duplicate messages**: Implement idempotency keys
- **Large message payloads**: Enforce character limits, chunking for future

**Edge Cases:**
- Airplane mode → Queue messages locally, sync when online
- Server downtime → Show offline indicator, queue messages
- Clock skew → Use server timestamps as source of truth
- Rapid message sending → Debounce and batch requests

**Acceptance Criteria:**
- [ ] Two emulators exchange echoed text; messages display in UI
- [ ] Messages appear in correct chronological order
- [ ] Network errors show user-friendly messages
- [ ] Offline messages queue and send when reconnected

### Step 6: Encrypt Transport
**Task:** Wrap messages in Signal cipher before POST; backend stores only ciphertext

**Error Scenarios & Handling:**
- **Encryption failures**: Fallback to error message, don't send plaintext
- **Key exchange failures**: Clear error with retry mechanism
- **Session corruption**: Detect and re-establish session
- **Decryption failures**: Show "Unable to decrypt" placeholder

**Edge Cases:**
- Recipient key rotation → Handle multiple key versions
- Concurrent sessions → Prevent race conditions in key updates
- Malformed ciphertext → Graceful error handling
- Key compromise detection → Force key regeneration

**Acceptance Criteria:**
- [ ] Logcat shows ciphertext payload; server DB doc has no readable text
- [ ] Messages decrypt correctly on recipient device
- [ ] Encryption/decryption errors are handled gracefully
- [ ] Performance impact < 50ms per message

### Step 7: Gemini Moderation Hook
**Task:** Backend calls Vertex AI, returns `allow|warn|block`

**Error Scenarios & Handling:**
- **API timeouts (>5s)**: Fail open with warning to user
- **Rate limiting**: Queue messages, implement backoff strategy
- **Invalid responses**: Log error, default to "warn" decision
- **Cost overruns**: Circuit breaker when daily budget exceeded

**Edge Cases:**
- Gemini service outage → Fail open with clear user notification
- Malformed JSON response → Parse errors handled gracefully
- Very long messages → Truncate or reject with clear error
- Non-English text → Handle unicode and different languages

**Acceptance Criteria:**
- [ ] Integration test: POST "You jerk" → JSON `block`; POST "Pick up at 5" → `allow`
- [ ] Timeout scenarios default to "warn" with user notification
- [ ] Cost monitoring alerts trigger before budget exceeded
- [ ] Response time p95 < 400ms

### Step 8: Warn/Block UX
**Task:** In Compose: yellow banner for `warn`, modal for `block` with "Edit & Resend"

**Error Scenarios & Handling:**
- **UI state corruption**: Reset to clean state on errors
- **Message editing failures**: Preserve original text, show error
- **Override confirmation**: Require explicit user acknowledgment
- **Accessibility issues**: Ensure screen readers work properly

**Edge Cases:**
- Very long messages → Scrollable edit dialog
- Rapid typing → Debounce re-moderation calls
- App backgrounding during edit → Preserve draft state
- Multiple warnings → Show cumulative warning count

**Acceptance Criteria:**
- [ ] Manual test: flagged sentence shows correct UI variant
- [ ] Edit dialog preserves formatting and cursor position
- [ ] Override warnings require explicit confirmation
- [ ] UI meets accessibility guidelines (TalkBack support)

### Step 9: Store Verdict + Hash in Firestore
**Task:** Save `{hash, verdict, ts}` per message

**Error Scenarios & Handling:**
- **Firestore write failures**: Retry with exponential backoff
- **Hash collision**: Use SHA-256 with message ID salt
- **Storage quota exceeded**: Implement data retention policies
- **Concurrent writes**: Use Firestore transactions

**Edge Cases:**
- Network partitions → Queue writes locally, sync when connected
- Large batch operations → Implement batching with size limits
- Data corruption → Verify hash integrity on read
- Legal hold requirements → Prevent accidental deletion

**Acceptance Criteria:**
- [ ] Firestore console shows new doc after each send; `hash` = SHA‑256(msg)
- [ ] Verdict data survives app/server restarts
- [ ] Hash verification detects any message tampering
- [ ] Write operations complete within 100ms p95

### Step 10: Push Notifications (FCM)
**Task:** Backend sends FCM to recipient on allowed messages

**Error Scenarios & Handling:**
- **FCM token expiration**: Refresh tokens automatically
- **Delivery failures**: Retry with exponential backoff
- **Invalid tokens**: Remove from database, request re-registration
- **Rate limiting**: Implement FCM quota management

**Edge Cases:**
- App uninstalled → Handle invalid registration gracefully
- Multiple devices → Send to all registered tokens
- Do Not Disturb mode → Respect system notification settings
- Background app restrictions → Use high-priority notifications

**Acceptance Criteria:**
- [ ] Phone locked: notification arrives within 5 s
- [ ] Notifications work across different Android versions
- [ ] Failed deliveries are logged and retried
- [ ] Notification content respects privacy (no message preview)

### Step 11: Basic PDF Export
**Task:** Cloud Function builds PDF from Firestore docs and returns signed URL

**Error Scenarios & Handling:**
- **PDF generation failures**: Retry with simplified format
- **Large exports**: Implement pagination and streaming
- **Storage failures**: Retry with different storage regions
- **URL expiration**: Clear communication of 24h limit

**Edge Cases:**
- Very long conversations → Split into multiple PDFs
- Special characters → Ensure proper UTF-8 encoding
- Missing messages → Show gaps clearly in export
- Concurrent export requests → Queue and rate limit

**Acceptance Criteria:**
- [ ] Tap *Export* → file downloads; opening PDF shows chat transcript
- [ ] PDF includes timestamps, verdict information, and legal disclaimers
- [ ] Export completes within 30 seconds for 1000 messages
- [ ] Generated PDFs are tamper-evident and legally admissible

### Step 12: Pilot Readiness Hardening
**Task:** Crashlytics enabled, p95 latency alert, secrets in Secret Manager

**Error Scenarios & Handling:**
- **Monitoring failures**: Redundant alerting channels
- **Secret rotation**: Zero-downtime key updates
- **Performance degradation**: Automatic scaling and circuit breakers
- **Security incidents**: Incident response procedures

**Edge Cases:**
- High load scenarios → Load testing and capacity planning
- Data privacy incidents → GDPR compliance procedures
- Third-party service outages → Fallback mechanisms
- Legal requests → Data export and retention procedures

**Acceptance Criteria:**
- [ ] Trigger test crash, see entry in Crashlytics; Cloud Monitoring shows uptime check green
- [ ] All secrets stored in Secret Manager with proper IAM
- [ ] Alerts fire within 5 minutes of SLA violations
- [ ] Security scanning passes with no high-severity issues

---

## Security Implementation Details

### Android App Security

#### Key Management (Android Keystore)
```kotlin
// Use Android Keystore for Signal identity keys
val keyGenParameterSpec = KeyGenParameterSpec.Builder(
    "signal_identity_key",
    KeyProperties.PURPOSE_SIGN or KeyProperties.PURPOSE_VERIFY
)
.setDigests(KeyProperties.DIGEST_SHA256)
.setSignaturePaddings(KeyProperties.SIGNATURE_PADDING_RSA_PKCS1)
.setUserAuthenticationRequired(true)  // Biometric/PIN required
.setUserAuthenticationValidityDurationSeconds(300)  // 5 min timeout
.setInvalidatedByBiometricEnrollment(true)  // Re-auth if fingerprints change
.build()
```

**Security Measures:**
- **Hardware Security Module**: Use TEE when available, software fallback with warnings
- **Key Attestation**: Verify keys are hardware-backed on supported devices
- **Biometric Authentication**: Require biometric unlock for key access
- **Key Rotation**: Implement 90-day rotation schedule for session keys

#### App Security Hardening
```kotlin
// Application class security setup
class CivilityApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        // Certificate pinning for backend
        val certificatePinner = CertificatePinner.Builder()
            .add("api.civility.app", "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
            .build()

        // Root detection
        if (RootBeer(this).isRooted) {
            showSecurityWarning("Device appears to be rooted")
        }

        // Debug detection
        if (BuildConfig.DEBUG && !isEmulator()) {
            throw SecurityException("Debug builds not allowed on real devices")
        }
    }
}
```

**Security Controls:**
- **Certificate Pinning**: Pin backend TLS certificates using OkHttp
- **Root Detection**: Warn users about security risks on rooted devices
- **Debug Protection**: Prevent debug builds on production devices
- **Screen Recording Protection**: Block screenshots in sensitive screens
- **App Integrity**: Use Play Integrity API to verify app authenticity

#### Network Security
```xml
<!-- res/xml/network_security_config.xml -->
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">civility.app</domain>
        <pin-set expiration="2026-01-01">
            <pin digest="SHA-256">AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</pin>
            <pin digest="SHA-256">BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=</pin>
        </pin-set>
    </domain-config>
</network-security-config>
```

### Backend Security (Cloud Run + Firestore)

#### Authentication & Authorization
```go
// JWT middleware with proper validation
func JWTMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := extractBearerToken(c.GetHeader("Authorization"))

        // Validate JWT with Google's public keys
        claims, err := validateJWT(token, os.Getenv("JWT_ISSUER"))
        if err != nil {
            c.JSON(401, gin.H{"error": "Invalid token"})
            c.Abort()
            return
        }

        // Rate limiting per user
        if !rateLimiter.Allow(claims.UserID) {
            c.JSON(429, gin.H{"error": "Rate limit exceeded"})
            c.Abort()
            return
        }

        c.Set("user_id", claims.UserID)
        c.Next()
    }
}
```

**Security Measures:**
- **JWT Validation**: Verify tokens using Google's public key rotation
- **Rate Limiting**: 100 requests/minute per user, 1000/minute global
- **CORS Policy**: Restrict to mobile app origins only
- **Request Validation**: Strict input validation with size limits

#### Secrets Management
```go
// Secret Manager integration
func getSecret(ctx context.Context, secretName string) (string, error) {
    client, err := secretmanager.NewClient(ctx)
    if err != nil {
        return "", err
    }
    defer client.Close()

    req := &secretmanagerpb.AccessSecretVersionRequest{
        Name: fmt.Sprintf("projects/%s/secrets/%s/versions/latest",
            os.Getenv("PROJECT_ID"), secretName),
    }

    result, err := client.AccessSecretVersion(ctx, req)
    if err != nil {
        return "", err
    }

    return string(result.Payload.Data), nil
}
```

**Secret Management:**
- **Google Secret Manager**: Store all API keys, database credentials
- **Automatic Rotation**: 90-day rotation for all secrets
- **Least Privilege**: Service accounts with minimal required permissions
- **Audit Logging**: All secret access logged to Cloud Audit Logs

#### Data Protection & Encryption
```go
// Message encryption before Firestore storage
func encryptForStorage(plaintext string, userID string) (string, error) {
    // Use AES-256-GCM with user-specific key derivation
    key := deriveStorageKey(userID, os.Getenv("MASTER_KEY"))

    block, err := aes.NewCipher(key)
    if err != nil {
        return "", err
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }

    nonce := make([]byte, gcm.NonceSize())
    if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }

    ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(ciphertext), nil
}
```

**Data Protection:**
- **Encryption at Rest**: AES-256-GCM for all message content in Firestore
- **Encryption in Transit**: TLS 1.3 with perfect forward secrecy
- **Key Derivation**: PBKDF2 with user-specific salts
- **Data Minimization**: Store only essential data, auto-delete plaintext after 30 days

#### Firestore Security Rules
```javascript
// Firestore security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Messages: users can only access conversations they're part of
    match /messages/{messageId} {
      allow read: if request.auth != null &&
        (resource.data.from_user_id == request.auth.uid ||
         resource.data.to_user_id == request.auth.uid);
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.from_user_id;
    }

    // Verdicts: read-only for message participants
    match /verdicts/{verdictId} {
      allow read: if request.auth != null &&
        exists(/databases/$(database)/documents/messages/$(resource.data.message_id)) &&
        (get(/databases/$(database)/documents/messages/$(resource.data.message_id)).data.from_user_id == request.auth.uid ||
         get(/databases/$(database)/documents/messages/$(resource.data.message_id)).data.to_user_id == request.auth.uid);
    }
  }
}
```

### Security Monitoring & Incident Response

#### Threat Detection
```go
// Security event logging
func logSecurityEvent(eventType string, userID string, details map[string]interface{}) {
    event := SecurityEvent{
        Type:      eventType,
        UserID:    userID,
        Timestamp: time.Now(),
        Details:   details,
        IPAddress: getClientIP(),
        UserAgent: getUserAgent(),
    }

    // Log to Cloud Logging with security label
    logger.WithFields(logrus.Fields{
        "security_event": true,
        "event_type":     eventType,
        "user_id":        userID,
    }).Warn("Security event detected", event)

    // Alert on critical events
    if isCriticalEvent(eventType) {
        sendSecurityAlert(event)
    }
}
```

**Monitoring Controls:**
- **Failed Authentication**: Alert after 5 failed attempts in 10 minutes
- **Unusual Access Patterns**: Detect access from new locations/devices
- **API Abuse**: Monitor for automated/scripted behavior
- **Data Exfiltration**: Alert on large export requests

#### Compliance & Privacy
```go
// GDPR data deletion
func deleteUserData(userID string) error {
    // 1. Delete user profile
    if err := deleteFirestoreDoc("users", userID); err != nil {
        return err
    }

    // 2. Anonymize messages (keep verdicts for legal retention)
    if err := anonymizeUserMessages(userID); err != nil {
        return err
    }

    // 3. Delete encryption keys
    if err := deleteUserKeys(userID); err != nil {
        return err
    }

    // 4. Log deletion for audit
    logSecurityEvent("user_data_deleted", userID, map[string]interface{}{
        "retention_period": "7_years_verdicts_only",
    })

    return nil
}
```

**Privacy Controls:**
- **Data Minimization**: Collect only necessary data for functionality
- **Retention Policies**: Auto-delete plaintext after 30 days, verdicts after 7 years
- **Right to Deletion**: GDPR-compliant user data deletion
- **Consent Management**: Clear opt-in for cloud-based moderation

### Security Testing & Validation

#### Automated Security Scanning
```yaml
# GitHub Actions security pipeline
- name: Security Scan
  run: |
    # SAST scanning
    semgrep --config=auto --error .

    # Dependency vulnerability scanning
    npm audit --audit-level=high

    # Container security scanning
    docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
      aquasec/trivy image civility-backend:latest

    # Infrastructure security
    checkov -f terraform/ --framework terraform
```

**Security Validation:**
- **SAST Scanning**: Semgrep for code vulnerabilities
- **Dependency Scanning**: npm audit + Snyk for known CVEs
- **Container Scanning**: Trivy for base image vulnerabilities
- **Infrastructure Scanning**: Checkov for Terraform misconfigurations

---

## MVP Testing Checklists

### Manual Testing for Each Implementation Step

#### Step 1-4: Basic Setup Validation
**Manual Checklist (5 minutes per step):**
```
□ Step 1: CI Pipeline
  - Push test commit → GitHub Actions runs without errors
  - Check build logs for any warnings
  - Verify Docker image builds successfully

□ Step 2: Android App
  - Install APK on test device
  - App launches without crashes
  - Main screen displays correctly

□ Step 3: Signal Keys
  - Check Android Keystore contains identity key
  - Verify biometric prompt appears (if supported)
  - Keys persist after app restart

□ Step 4: Backend Health
  - curl https://your-app.run.app/healthz
  - Returns {"status":"ok"} with 200 status
  - Response time < 1 second
```

#### Step 5-8: Core Messaging Flow
**Manual Test Script (10 minutes):**
```
□ Step 5: Basic Messaging
  Setup: Two test devices/emulators
  1. Device A: Type "Hello test" → Send
  2. Device B: Should receive "Hello test"
  3. Check: Message appears in correct order
  4. Check: Timestamps are reasonable

□ Step 6: Encryption
  1. Send message from Device A
  2. Check Firestore console: message doc contains ciphertext (not plaintext)
  3. Device B receives and displays original text correctly
  4. Verify: No readable text in network logs

□ Step 7: Moderation
  Test messages:
  - "Pick up at 5pm" → Should be ALLOWED
  - "You're an idiot" → Should be BLOCKED
  - "That's unreasonable" → Should be WARNED
  Check: Gemini responses match expectations

□ Step 8: Warning/Block UI
  1. Send blocked message → Modal appears with "Edit & Resend"
  2. Send warned message → Yellow banner with "Send Anyway"
  3. Edit blocked message → Re-moderation occurs
  4. Override warning → Message sends with warning flag
```

#### Step 9-12: Advanced Features
**Manual Validation (15 minutes):**
```
□ Step 9: Verdict Storage
  1. Send any message
  2. Check Firestore: verdicts/{id} document created
  3. Verify: hash = SHA-256 of original message
  4. Check: timestamp and decision fields populated

□ Step 10: Push Notifications
  1. Lock recipient device
  2. Send message from sender
  3. Notification appears within 5 seconds
  4. Tap notification → Opens to conversation

□ Step 11: PDF Export
  1. Send 3-5 test messages
  2. Tap Settings → Export Conversation
  3. Wait for download link (< 30 seconds)
  4. Open PDF: Contains messages, timestamps, hashes

□ Step 12: Monitoring
  1. Trigger test crash: throw new Error("test")
  2. Check Crashlytics dashboard for crash report
  3. Verify Cloud Monitoring shows uptime check green
  4. Check logs for any errors
```

### Essential Error Testing

**Network Failure Recovery (5 minutes):**
```
□ Airplane Mode Test
  1. Turn on airplane mode
  2. Try to send message
  3. Should show "queued" status
  4. Turn off airplane mode
  5. Message should auto-retry and deliver

□ Backend Downtime
  1. Temporarily stop Cloud Run service
  2. Try to send message
  3. Should show error with retry option
  4. Restart service
  5. Retry should work
```

**Security Basics (5 minutes):**
```
□ Biometric Lock
  1. Enable biometric in settings
  2. Close and reopen app
  3. Biometric prompt should appear
  4. Wrong biometric → Access denied
  5. Correct biometric → App unlocks

□ Certificate Pinning
  1. Try to connect with invalid certificate
  2. Should fail with security error
  3. Valid certificate should work normally
```

### Simple Performance Check

**Basic Latency Test (2 minutes):**
```
□ Message Send Speed
  1. Start timer
  2. Send message
  3. Stop timer when recipient receives
  4. Should be < 2 seconds for normal message
  5. Should be < 5 seconds for moderated message

□ App Startup
  1. Force close app
  2. Start timer
  3. Launch app
  4. Stop when main screen appears
  5. Should be < 3 seconds
```

### MVP Test Environment

**Simple Test Setup:**
```bash
# Test data setup
export TEST_EMAIL_1="<EMAIL>"
export TEST_EMAIL_2="<EMAIL>"
export TEST_PROJECT="civility-dev"

# Quick test script
./scripts/run-mvp-tests.sh
```

**Test Script Content:**
```bash
#!/bin/bash
echo "🧪 Running MVP smoke tests..."

# 1. Backend health check
curl -f https://civility-dev.run.app/healthz || exit 1

# 2. Database connectivity
gcloud firestore databases list --project=civility-dev || exit 1

# 3. Gemini API test
curl -X POST https://civility-dev.run.app/test/moderate \
  -d '{"message":"test"}' || exit 1

echo "✅ All smoke tests passed!"
```

### Post-MVP Testing (Future)
*Save comprehensive integration tests for Phase 2:*
- Automated UI testing with Espresso
- Load testing with multiple concurrent users
- Security penetration testing
- Cross-device compatibility testing
- Performance benchmarking under load

---

## Minimal Performance Monitoring (MVP)

### Basic GCP Monitoring Setup

#### Cloud Monitoring (Built-in, Free Tier)
```yaml
# Basic uptime check configuration
uptime_checks:
  - name: "civility-backend-health"
    monitored_resource:
      type: "uptime_url"
      labels:
        host: "civility-dev.run.app"
    http_check:
      path: "/healthz"
      port: 443
      use_ssl: true
    period: "60s"
    timeout: "10s"

  - name: "civility-backend-latency"
    monitored_resource:
      type: "uptime_url"
      labels:
        host: "civility-dev.run.app"
    http_check:
      path: "/messages/send"
      port: 443
      use_ssl: true
      headers:
        Authorization: "Bearer test-token"
    period: "300s"  # Every 5 minutes
    timeout: "5s"
```

#### Essential Alerts (Email Only)
```go
// Simple alerting in backend code
func setupBasicAlerts() {
    // 1. Cost alert - Gemini spend
    if dailyGeminiCost > 10.00 {
        sendEmail("<EMAIL>", "Daily Gemini cost exceeded $10")
    }

    // 2. Error rate alert - > 5% errors in 10 minutes
    if errorRate > 0.05 {
        sendEmail("<EMAIL>", "High error rate detected")
    }

    // 3. Latency alert - > 2 seconds average
    if avgLatency > 2000 {
        sendEmail("<EMAIL>", "High latency detected")
    }
}
```

### Simple Logging Strategy

#### Structured Logging (Cloud Logging)
```go
// Essential log events only
func logEssentialEvents(event string, data map[string]interface{}) {
    log := logrus.WithFields(logrus.Fields{
        "event":     event,
        "timestamp": time.Now().Unix(),
        "service":   "civility-backend",
    })

    switch event {
    case "message_sent":
        log.WithFields(logrus.Fields{
            "user_id":         data["user_id"],
            "moderation_time": data["moderation_ms"],
            "verdict":         data["verdict"],
        }).Info("Message processed")

    case "gemini_error":
        log.WithFields(logrus.Fields{
            "error":    data["error"],
            "retry_count": data["retries"],
        }).Error("Gemini API failure")

    case "cost_tracking":
        log.WithFields(logrus.Fields{
            "daily_cost":    data["cost_usd"],
            "message_count": data["messages"],
        }).Info("Daily cost summary")
    }
}
```

#### Android Crash Reporting (Crashlytics)
```kotlin
// Minimal crash reporting setup
class CivilityApplication : Application() {
    override fun onCreate() {
        super.onCreate()

        // Enable Crashlytics (free tier)
        FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(true)

        // Log key user actions for debugging
        FirebaseCrashlytics.getInstance().setCustomKey("user_id", getCurrentUserId())
        FirebaseCrashlytics.getInstance().setCustomKey("app_version", BuildConfig.VERSION_NAME)
    }
}

// Log non-fatal errors
fun logNonFatalError(error: Exception, context: String) {
    FirebaseCrashlytics.getInstance().recordException(error)
    FirebaseCrashlytics.getInstance().log("Context: $context")
}
```

### Cost Monitoring

#### Simple Cost Tracking
```go
// Track Gemini API costs
type CostTracker struct {
    DailySpend   float64
    MessageCount int
    LastReset    time.Time
}

func (ct *CostTracker) trackGeminiCall(inputTokens, outputTokens int) {
    // Gemini 2.5 Flash-Lite pricing (as of 2025)
    inputCost := float64(inputTokens) * 0.000075 / 1000   // $0.075 per 1K input tokens
    outputCost := float64(outputTokens) * 0.0003 / 1000   // $0.30 per 1K output tokens

    ct.DailySpend += inputCost + outputCost
    ct.MessageCount++

    // Reset daily counter
    if time.Since(ct.LastReset) > 24*time.Hour {
        logEssentialEvents("cost_tracking", map[string]interface{}{
            "cost_usd": ct.DailySpend,
            "messages": ct.MessageCount,
        })

        ct.DailySpend = 0
        ct.MessageCount = 0
        ct.LastReset = time.Now()
    }

    // Simple alert
    if ct.DailySpend > 10.0 {
        sendCostAlert(ct.DailySpend, ct.MessageCount)
    }
}
```

### Performance Validation

#### Manual Performance Checks
```bash
#!/bin/bash
# scripts/check-performance.sh - Run weekly during MVP

echo "🔍 Checking MVP performance..."

# 1. Backend latency check
echo "Testing backend latency..."
for i in {1..10}; do
    curl -w "%{time_total}\n" -o /dev/null -s https://civility-dev.run.app/healthz
done | awk '{sum+=$1; count++} END {print "Average latency:", sum/count "s"}'

# 2. Gemini moderation speed
echo "Testing moderation latency..."
curl -w "%{time_total}\n" -X POST https://civility-dev.run.app/test/moderate \
  -H "Content-Type: application/json" \
  -d '{"message":"This is a test message"}' \
  -o /dev/null -s

# 3. Check daily costs
echo "Checking costs..."
gcloud logging read 'resource.type="cloud_run_revision" AND jsonPayload.event="cost_tracking"' \
  --limit=1 --format="value(jsonPayload.cost_usd)"

echo "✅ Performance check complete"
```

### Minimal Dashboard Setup

#### Simple Cloud Monitoring Dashboard
```json
{
  "displayName": "Civility MVP Dashboard",
  "mosaicLayout": {
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Backend Uptime",
          "scorecard": {
            "timeSeriesQuery": {
              "unitOverride": "1",
              "outputFullDuration": false,
              "timeSeriesFilter": {
                "filter": "resource.type=\"uptime_url\"",
                "aggregation": {
                  "alignmentPeriod": "300s",
                  "perSeriesAligner": "ALIGN_FRACTION_TRUE"
                }
              }
            },
            "sparkChartView": {
              "sparkChartType": "SPARK_LINE"
            }
          }
        }
      },
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "Message Processing Rate",
          "xyChart": {
            "dataSets": [{
              "timeSeriesQuery": {
                "timeSeriesFilter": {
                  "filter": "resource.type=\"cloud_run_revision\" AND jsonPayload.event=\"message_sent\"",
                  "aggregation": {
                    "alignmentPeriod": "300s",
                    "perSeriesAligner": "ALIGN_RATE"
                  }
                }
              }
            }]
          }
        }
      }
    ]
  }
}
```

### MVP Monitoring Checklist

**Weekly Manual Review (10 minutes):**
```
□ Check Cloud Monitoring dashboard
  - Uptime > 99% for the week
  - No critical alerts fired
  - Error rate < 5%

□ Review cost tracking
  - Daily Gemini spend < $1
  - Cost per message < $0.005
  - No unexpected spikes

□ Check crash reports
  - < 1% crash rate in Crashlytics
  - No critical user-blocking issues
  - Review top crash patterns

□ Performance spot check
  - Run performance script
  - Average latency < 2 seconds
  - Moderation time < 5 seconds
```

**What We're NOT Doing (Post-MVP):**
- Custom metrics and complex dashboards
- Detailed performance profiling
- Load testing and capacity planning
- Advanced alerting and on-call rotation
- APM tools like New Relic or DataDog

---

## Technical Specifications

### Authentication System
**Flow:** Email + OTP → Device Registration → Key Generation
```
1. User enters email → Backend sends 6-digit OTP via SendGrid
2. OTP verification → Backend creates user record in Firestore
3. App generates Signal identity key pair in Android Keystore
4. Public key uploaded to backend user profile
5. Biometric unlock configured (optional)
```

**API Endpoints:**
- `POST /auth/request-otp` → `{email}` → `{success, message}`
- `POST /auth/verify-otp` → `{email, otp}` → `{jwt_token, user_id}`
- `POST /auth/register-device` → `{public_key, fcm_token}` → `{success}`

**Firestore Schema:**
```
users/{user_id}
├── email: string
├── created_at: timestamp
├── public_key: base64
├── fcm_token: string
└── status: "active" | "suspended"
```

### Messaging System
**Signal Protocol Implementation:**
```
1. Sender encrypts: plaintext → Signal.encrypt(recipient_public_key) → ciphertext
2. Backend receives: {to_user_id, ciphertext, sender_signature}
3. Moderation: decrypt → analyze → re-encrypt if allowed
4. Delivery: FCM push → recipient decrypts locally
```

**Message States:**
- `pending_moderation` → Gemini analysis in progress
- `blocked` → Sender can edit and resend
- `warned` → Sender can override and send
- `delivered` → Recipient can view (collapsed if toxic)
- `read` → Read receipt sent

**API Endpoints:**
- `POST /messages/send` → `{to_user_id, ciphertext}` → `{message_id, status}`
- `GET /messages/inbox` → `{messages: [{id, from, ciphertext, verdict, timestamp}]}`
- `POST /messages/mark-read` → `{message_id}` → `{success}`

### Moderation System
**Gemini Integration:**
```
System Prompt: "Analyze this co-parenting message for toxicity. Respond with JSON only."
User Message: [decrypted text]
Expected Response: {
  "decision": "allow" | "warn" | "block",
  "labels": ["hostile", "manipulative", "threatening"],
  "confidence": 0.85
}
```

**Verdict Storage:**
```
verdicts/{message_id}
├── hash: sha256(original_message)
├── decision: string
├── labels: array
├── confidence: number
├── timestamp: timestamp
└── model_version: "gemini-2.5-flash-lite"
```

**Error Handling:**
- Gemini timeout (>5s) → fail open with warning
- API quota exceeded → queue for retry, notify sender
- Invalid JSON response → log error, default to "warn"

---

## Complete Data Models & API Contracts

### Firestore Collections Schema

#### Users Collection
```typescript
users/{user_id} {
  email: string;                    // "<EMAIL>"
  created_at: Timestamp;
  updated_at: Timestamp;
  public_key: string;               // Base64 Signal identity key
  fcm_token: string;                // Firebase messaging token
  status: "active" | "suspended" | "deleted";
  profile: {
    display_name: string;           // "Sarah M."
    timezone: string;               // "America/New_York"
  };
  settings: {
    biometric_enabled: boolean;
    notification_enabled: boolean;
    auto_collapse_toxic: boolean;   // Default: true
  };
  // Co-parent relationship
  connected_user_id?: string;       // Other parent's user_id
  connection_status: "pending" | "connected" | "blocked";
}
```

#### Messages Collection
```typescript
messages/{message_id} {
  from_user_id: string;
  to_user_id: string;
  ciphertext: string;               // Signal-encrypted message
  sender_signature: string;         // Message authenticity
  created_at: Timestamp;
  status: "pending_moderation" | "blocked" | "warned" | "delivered" | "read";
  verdict_id?: string;              // Reference to verdicts collection
  // Metadata for export/legal purposes
  sequence_number: number;          // Ordered within conversation
  reply_to_message_id?: string;     // For threading (future)
}
```

#### Verdicts Collection
```typescript
verdicts/{verdict_id} {
  message_id: string;
  hash: string;                     // SHA-256 of original plaintext
  decision: "allow" | "warn" | "block";
  labels: string[];                 // ["hostile", "manipulative"]
  confidence: number;               // 0.0 - 1.0
  model_version: string;            // "gemini-2.5-flash-lite"
  processing_time_ms: number;       // For performance monitoring
  created_at: Timestamp;
  // Legal retention (7 years)
  retention_until: Timestamp;
}
```

#### Conversations Collection (Derived)
```typescript
conversations/{conversation_id} {  // conversation_id = sorted(user1_id, user2_id).join("_")
  participants: string[];           // [user1_id, user2_id]
  created_at: Timestamp;
  last_message_at: Timestamp;
  last_message_preview: string;     // Encrypted preview for notifications
  message_count: number;
  unread_count: {
    [user_id: string]: number;
  };
}
```

#### Export Jobs Collection
```typescript
export_jobs/{job_id} {
  user_id: string;                  // Who requested export
  conversation_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  date_range: {
    start: Timestamp;
    end: Timestamp;
  };
  file_url?: string;                // Signed Cloud Storage URL
  expires_at?: Timestamp;           // 24h expiry
  created_at: Timestamp;
  completed_at?: Timestamp;
}
```

### Complete API Specification

#### Authentication Endpoints

**POST /auth/request-otp**
```typescript
Request: {
  email: string;                    // Valid email format
}
Response: {
  success: boolean;
  message: string;                  // "OTP sent to email"
  rate_limit_reset?: number;        // Unix timestamp if rate limited
}
Errors: 400 (invalid email), 429 (rate limit), 500 (send failure)
```

**POST /auth/verify-otp**
```typescript
Request: {
  email: string;
  otp: string;                      // 6-digit code
}
Response: {
  success: boolean;
  jwt_token: string;                // 24h expiry
  user_id: string;
  is_new_user: boolean;             // First time login
}
Errors: 400 (invalid OTP), 401 (expired), 429 (rate limit)
```

**POST /auth/register-device**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  public_key: string;               // Base64 Signal identity key
  fcm_token: string;
  device_info: {
    platform: "android";
    app_version: string;
    os_version: string;
  };
}
Response: {
  success: boolean;
  user_profile: UserProfile;        // Full user object
}
```

**POST /auth/connect-coparent**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  coparent_email: string;
}
Response: {
  success: boolean;
  connection_status: "pending" | "connected";
  message: string;
}
```

#### Messaging Endpoints

**POST /messages/send**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  to_user_id: string;
  ciphertext: string;               // Signal-encrypted message
  sender_signature: string;
  client_timestamp: number;         // Unix timestamp
}
Response: {
  message_id: string;
  status: "pending_moderation" | "blocked" | "warned" | "delivered";
  verdict?: {
    decision: string;
    labels: string[];
    confidence: number;
  };
  estimated_delivery_time?: number; // Seconds
}
Errors: 400 (invalid payload), 403 (blocked user), 413 (message too long)
```

**GET /messages/conversation/{conversation_id}**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Query: {
  limit?: number;                   // Default: 50, Max: 100
  before_timestamp?: number;        // Pagination
  include_verdicts?: boolean;       // Default: false
}
Response: {
  messages: Message[];
  has_more: boolean;
  next_cursor?: string;
}
```

**POST /messages/mark-read**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  message_ids: string[];            // Batch mark as read
}
Response: {
  success: boolean;
  updated_count: number;
}
```

**POST /messages/override-warning**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  message_id: string;               // Message with "warned" status
  user_acknowledged: boolean;       // User confirmed they want to send
}
Response: {
  success: boolean;
  new_status: "delivered";
}
```

#### Export & Legal Endpoints

**POST /export/request**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Request: {
  conversation_id: string;
  date_range: {
    start: string;                  // ISO 8601 date
    end: string;
  };
  format: "pdf" | "json" | "both";
  include_verdicts: boolean;        // Include moderation data
}
Response: {
  job_id: string;
  estimated_completion: number;     // Seconds
  status: "pending";
}
```

**GET /export/status/{job_id}**
```typescript
Headers: { Authorization: "Bearer <jwt_token>" }
Response: {
  job_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  progress_percent?: number;        // 0-100
  file_urls?: {
    pdf?: string;                   // Signed URL, 24h expiry
    json?: string;
  };
  error_message?: string;
  expires_at?: string;              // ISO 8601
}
```

#### Admin & Monitoring Endpoints

**GET /health**
```typescript
Response: {
  status: "ok" | "degraded" | "down";
  timestamp: number;
  services: {
    firestore: "ok" | "error";
    gemini: "ok" | "error";
    fcm: "ok" | "error";
  };
  version: string;
}
```

**GET /metrics** (Internal only)
```typescript
Headers: { X-API-Key: "<internal_key>" }
Response: {
  messages_per_minute: number;
  moderation_latency_p95: number;   // Milliseconds
  error_rate: number;               // 0.0 - 1.0
  active_users_24h: number;
  gemini_cost_today: number;        // USD
}
```

### Error Response Format
```typescript
// All endpoints return errors in this format
ErrorResponse: {
  error: {
    code: string;                   // "INVALID_REQUEST", "RATE_LIMITED"
    message: string;                // Human-readable description
    details?: any;                  // Additional context
    request_id: string;             // For debugging
  };
}
```

### WebSocket Events (Real-time)
```typescript
// Client subscribes to: /ws/conversations/{conversation_id}

// Incoming message
{
  type: "message_received";
  data: {
    message_id: string;
    from_user_id: string;
    ciphertext: string;
    timestamp: number;
    verdict?: Verdict;
  };
}

// Typing indicator
{
  type: "typing_start" | "typing_stop";
  data: {
    user_id: string;
    timestamp: number;
  };
}

// Read receipt
{
  type: "message_read";
  data: {
    message_id: string;
    read_by: string;
    timestamp: number;
  };
}
```
```
```

## Post‑MVP / Phase 2 (not in this sprint)
- Sender suggested rewrites & auto‑rewrite modes.
- Observer (therapist/legal) read‑only accounts.
- Group chat & media attachments.
- Optional PII redaction toggle.

*Last updated: 2025‑07‑01*

