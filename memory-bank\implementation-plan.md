# Civility — Implementation Plan (MVP)

> **Focus:** deliver core 1‑to‑1 chat with Gemini moderation and E2EE. Nice‑to‑have features (auto‑rewrite mode, observer roles, etc.) come in Phase 2.

| Step | Task (small & focused) | Simple “Done” Test |
| ---- | ---------------------- | ------------------- |
| **1** | **Repo & CI bootstrap** – Create monorepo (`/android`, `/backend`) + GitHub Actions pipeline (lint → unit tests → Docker build). | Push a PR with `README.md`; CI passes all jobs. |
| **2** | **Android skeleton** – Init Kotlin/Compose project, single `MainActivity`. | `./gradlew installDebug` launches blank app on Pixel 8. |
| **3** | **Signal key setup** – Generate and persist Curve25519 identity + session keys (libsignal‑protocol‑java). | Unit test: serialize keys, reload, compare public key bytes. |
| **4** | **Minimal Cloud Run backend** – Gin/Fastify server with `/healthz` endpoint. | `curl https://<url>/healthz` returns `{status:"ok"}` with 200. |
| **5** | **Send/receive plaintext messages (no moderation yet)** – Retrofit POST to `/msg`, backend echoes with timestamp. | Two emulators exchange echoed text; messages display in UI. |
| **6** | **Encrypt transport** – Wrap messages in Signal cipher before POST; backend stores only ciphertext. | Logcat shows ciphertext payload; server DB doc has no readable text. |
| **7** | **Gemini moderation hook** – Backend calls Vertex AI, returns `allow|warn|block`. | Integration test: POST “You jerk” → JSON `block`; POST “Pick up at 5” → `allow`. |
| **8** | **Warn/Block UX** – In Compose: yellow banner for `warn`, modal for `block` with “Edit & Resend”. | Manual test: flagged sentence shows correct UI variant. |
| **9** | **Store verdict + hash in Firestore** – Save `{hash, verdict, ts}` per msg. | Firestore console shows new doc after each send; `hash` = SHA‑256(msg). |
| **10** | **Push notifications (FCM)** – Backend sends FCM to recipient on allowed msg. | Phone locked: notification arrives within 5 s. |
| **11** | **Basic PDF export** – Cloud Function builds PDF from Firestore docs and returns signed URL. | Tap *Export* → file downloads; opening PDF shows chat transcript. |
| **12** | **Pilot readiness hardening** – Crashlytics enabled, p95 latency alert, secrets in Secret Manager. | Trigger test crash, see entry in Crashlytics; Cloud Monitoring shows uptime check green. |

## Post‑MVP / Phase 2 (not in this sprint)
- Sender suggested rewrites & auto‑rewrite modes.
- Observer (therapist/legal) read‑only accounts.
- Group chat & media attachments.
- Optional PII redaction toggle.

*Last updated: 2025‑07‑01*

