# Civility — Implementation Plan (MVP)

> **Focus:** deliver core 1‑to‑1 chat with Gemini moderation and E2EE. Nice‑to‑have features (auto‑rewrite mode, observer roles, etc.) come in Phase 2.

| Step | Task (small & focused) | Simple “Done” Test |
| ---- | ---------------------- | ------------------- |
| **1** | **Repo & CI bootstrap** – Create monorepo (`/android`, `/backend`) + GitHub Actions pipeline (lint → unit tests → Docker build). | Push a PR with `README.md`; CI passes all jobs. |
| **2** | **Android skeleton** – Init Kotlin/Compose project, single `MainActivity`. | `./gradlew installDebug` launches blank app on Pixel 8. |
| **3** | **Signal key setup** – Generate and persist Curve25519 identity + session keys (libsignal‑protocol‑java). | Unit test: serialize keys, reload, compare public key bytes. |
| **4** | **Minimal Cloud Run backend** – Gin/Fastify server with `/healthz` endpoint. | `curl https://<url>/healthz` returns `{status:"ok"}` with 200. |
| **5** | **Send/receive plaintext messages (no moderation yet)** – Retrofit POST to `/msg`, backend echoes with timestamp. | Two emulators exchange echoed text; messages display in UI. |
| **6** | **Encrypt transport** – Wrap messages in Signal cipher before POST; backend stores only ciphertext. | Logcat shows ciphertext payload; server DB doc has no readable text. |
| **7** | **Gemini moderation hook** – Backend calls Vertex AI, returns `allow|warn|block`. | Integration test: POST “You jerk” → JSON `block`; POST “Pick up at 5” → `allow`. |
| **8** | **Warn/Block UX** – In Compose: yellow banner for `warn`, modal for `block` with “Edit & Resend”. | Manual test: flagged sentence shows correct UI variant. |
| **9** | **Store verdict + hash in Firestore** – Save `{hash, verdict, ts}` per msg. | Firestore console shows new doc after each send; `hash` = SHA‑256(msg). |
| **10** | **Push notifications (FCM)** – Backend sends FCM to recipient on allowed msg. | Phone locked: notification arrives within 5 s. |
| **11** | **Basic PDF export** – Cloud Function builds PDF from Firestore docs and returns signed URL. | Tap *Export* → file downloads; opening PDF shows chat transcript. |
| **12** | **Pilot readiness hardening** – Crashlytics enabled, p95 latency alert, secrets in Secret Manager. | Trigger test crash, see entry in Crashlytics; Cloud Monitoring shows uptime check green. |

---

## Technical Specifications

### Authentication System
**Flow:** Email + OTP → Device Registration → Key Generation
```
1. User enters email → Backend sends 6-digit OTP via SendGrid
2. OTP verification → Backend creates user record in Firestore
3. App generates Signal identity key pair in Android Keystore
4. Public key uploaded to backend user profile
5. Biometric unlock configured (optional)
```

**API Endpoints:**
- `POST /auth/request-otp` → `{email}` → `{success, message}`
- `POST /auth/verify-otp` → `{email, otp}` → `{jwt_token, user_id}`
- `POST /auth/register-device` → `{public_key, fcm_token}` → `{success}`

**Firestore Schema:**
```
users/{user_id}
├── email: string
├── created_at: timestamp
├── public_key: base64
├── fcm_token: string
└── status: "active" | "suspended"
```

### Messaging System
**Signal Protocol Implementation:**
```
1. Sender encrypts: plaintext → Signal.encrypt(recipient_public_key) → ciphertext
2. Backend receives: {to_user_id, ciphertext, sender_signature}
3. Moderation: decrypt → analyze → re-encrypt if allowed
4. Delivery: FCM push → recipient decrypts locally
```

**Message States:**
- `pending_moderation` → Gemini analysis in progress
- `blocked` → Sender can edit and resend
- `warned` → Sender can override and send
- `delivered` → Recipient can view (collapsed if toxic)
- `read` → Read receipt sent

**API Endpoints:**
- `POST /messages/send` → `{to_user_id, ciphertext}` → `{message_id, status}`
- `GET /messages/inbox` → `{messages: [{id, from, ciphertext, verdict, timestamp}]}`
- `POST /messages/mark-read` → `{message_id}` → `{success}`

### Moderation System
**Gemini Integration:**
```
System Prompt: "Analyze this co-parenting message for toxicity. Respond with JSON only."
User Message: [decrypted text]
Expected Response: {
  "decision": "allow" | "warn" | "block",
  "labels": ["hostile", "manipulative", "threatening"],
  "confidence": 0.85
}
```

**Verdict Storage:**
```
verdicts/{message_id}
├── hash: sha256(original_message)
├── decision: string
├── labels: array
├── confidence: number
├── timestamp: timestamp
└── model_version: "gemini-2.5-flash-lite"
```

**Error Handling:**
- Gemini timeout (>5s) → fail open with warning
- API quota exceeded → queue for retry, notify sender
- Invalid JSON response → log error, default to "warn"

## Post‑MVP / Phase 2 (not in this sprint)
- Sender suggested rewrites & auto‑rewrite modes.
- Observer (therapist/legal) read‑only accounts.
- Group chat & media attachments.
- Optional PII redaction toggle.

*Last updated: 2025‑07‑01*

